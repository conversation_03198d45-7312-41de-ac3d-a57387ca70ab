import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: import.meta.env.VITE_OPENAI_KEY,
  dangerouslyAllowBrowser: true // Note: In production, API calls should be made from backend
});

export interface CopyGenerationRequest {
  topic: string;
  platforms: string[];
  tone: string;
}

export interface GeneratedCopy {
  platform: string;
  variations: string[];
}

// Platform-specific content guidelines
const platformGuidelines = {
  instagram: {
    maxLength: 2200,
    characteristics: "Visual-focused, hashtag-friendly, engaging captions, story-driven",
    format: "Short paragraphs, emojis, call-to-action"
  },
  facebook: {
    maxLength: 500,
    characteristics: "Community-focused, conversational, shareable content",
    format: "Engaging questions, relatable content, clear messaging"
  },
  linkedin: {
    maxLength: 1300,
    characteristics: "Professional, industry insights, thought leadership",
    format: "Professional tone, industry terminology, networking focus"
  },
  twitter: {
    maxLength: 280,
    characteristics: "Concise, trending topics, real-time engagement",
    format: "Brief, punchy, hashtag integration, thread-worthy"
  },
  youtube: {
    maxLength: 5000,
    characteristics: "Video-focused, descriptive, SEO-optimized",
    format: "Detailed descriptions, timestamps, call-to-subscribe"
  }
};

// Tone definitions
const toneDefinitions = {
  professional: "formal, authoritative, industry-focused, credible",
  friendly: "warm, approachable, conversational, welcoming",
  funny: "humorous, witty, entertaining, light-hearted",
  persuasive: "compelling, action-oriented, convincing, motivational",
  educational: "informative, clear, instructional, helpful",
  inspiring: "motivational, uplifting, empowering, aspirational"
};

export async function generateCopy(request: CopyGenerationRequest): Promise<Record<string, string[]>> {
  try {
    const results: Record<string, string[]> = {};

    // Generate content for each platform
    for (const platform of request.platforms) {
      const platformInfo = platformGuidelines[platform as keyof typeof platformGuidelines];
      const toneDescription = toneDefinitions[request.tone as keyof typeof toneDefinitions];

      const prompt = `You are an expert copywriter and content creator specializing in social media marketing. Your task is to create engaging, platform-specific content that drives engagement and achieves marketing objectives.

CONTENT BRIEF:
- Topic/Theme: ${request.topic}
- Platform: ${platform.toUpperCase()}
- Tone: ${toneDescription}
- Target: Generate 3 unique variations

PLATFORM SPECIFICATIONS for ${platform.toUpperCase()}:
- Character limit: ${platformInfo.maxLength} characters
- Platform characteristics: ${platformInfo.characteristics}
- Content format: ${platformInfo.format}

REQUIREMENTS:
1. Create 3 distinct variations of content for the same topic
2. Each variation should have a different approach or angle
3. Optimize for ${platform} audience behavior and engagement patterns
4. Include relevant hashtags where appropriate (especially for Instagram and Twitter)
5. Ensure content is ${toneDescription} in tone
6. Make content actionable and engaging
7. Consider platform-specific features (Stories, Reels, etc. for Instagram; threads for Twitter; etc.)
8. Write in the same language as the topic provided by the user

OUTPUT FORMAT:
Provide exactly 3 variations, each as a complete, ready-to-post piece of content.

CRITICAL: You must separate each variation clearly. Use this exact format:

VARIATION 1:
[Complete first post content here - no additional text]

VARIATION 2:
[Complete second post content here - no additional text]

VARIATION 3:
[Complete third post content here - no additional text]

Do NOT include any other text outside of the variations. Each variation should be a standalone, complete post.

Generate the content now:`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: "You are an expert copywriter and social media content creator. You create engaging, platform-optimized content that drives engagement and achieves marketing objectives. Always respond in the same language as the user's input."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 1500,
        temperature: 0.8,
      });

      const content = completion.choices[0]?.message?.content || '';

      // Split the content into variations
      let variations: string[] = [];

      // Try different splitting patterns
      const patterns = [
        /VARIATION \d+:/i,
        /---VARIATION \d+---/i,
        /\d+\.\s*\*\*/,
        /\*\*\d+\./
      ];

      for (const pattern of patterns) {
        const split = content.split(pattern);
        if (split.length > 1) {
          variations = split
            .slice(1) // Remove the first empty element
            .map(variation => variation.trim())
            .filter(variation => variation.length > 0)
            .slice(0, 3);
          break;
        }
      }

      // If no pattern worked, try to extract content manually
      if (variations.length === 0) {
        // Look for numbered content or bullet points
        const lines = content.split('\n');
        let currentVariation = '';
        let variationCount = 0;

        for (const line of lines) {
          if (line.match(/^\d+[\.\)]/)) {
            if (currentVariation.trim()) {
              variations.push(currentVariation.trim());
              variationCount++;
            }
            currentVariation = line;
          } else {
            currentVariation += '\n' + line;
          }

          if (variationCount >= 3) break;
        }

        if (currentVariation.trim()) {
          variations.push(currentVariation.trim());
        }
      }

      // If still no variations, use the whole content as one variation
      if (variations.length === 0) {
        variations = [content.trim()];
      }

      // Ensure we have exactly 3 variations
      variations = variations.slice(0, 3);
      while (variations.length < 3) {
        variations.push(variations[0] || `Conteúdo gerado para ${platform} sobre ${request.topic}`);
      }

      results[platform] = variations;
    }

    return results;
  } catch (error) {
    console.error('Error generating copy:', error);
    throw new Error('Failed to generate copy. Please check your API key and try again.');
  }
}

// Function to regenerate a specific variation
export async function regenerateVariation(
  topic: string, 
  platform: string, 
  tone: string, 
  currentVariation: string
): Promise<string> {
  try {
    const platformInfo = platformGuidelines[platform as keyof typeof platformGuidelines];
    const toneDescription = toneDefinitions[tone as keyof typeof toneDefinitions];

    const prompt = `You are an expert copywriter. Create a NEW variation of content that is different from the current one.

CONTENT BRIEF:
- Topic: ${topic}
- Platform: ${platform.toUpperCase()}
- Tone: ${toneDescription}
- Current variation to improve/change: "${currentVariation}"

PLATFORM SPECIFICATIONS:
- Character limit: ${platformInfo.maxLength} characters
- Platform characteristics: ${platformInfo.characteristics}
- Content format: ${platformInfo.format}

Create a completely new variation that:
1. Covers the same topic but with a different angle or approach
2. Maintains the ${toneDescription} tone
3. Is optimized for ${platform}
4. Is distinctly different from the current variation
5. Includes appropriate hashtags if relevant
6. Write in the same language as the topic provided

Provide only the new content variation:`;

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "You are an expert copywriter and social media content creator. Always respond in the same language as the user's input."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 500,
      temperature: 0.9,
    });

    return completion.choices[0]?.message?.content?.trim() || currentVariation;
  } catch (error) {
    console.error('Error regenerating variation:', error);
    throw new Error('Failed to regenerate content. Please try again.');
  }
}
